import axiosClient from '../axios-client';
import generalAxiosClient from '../../general-axios-client';
import { ApiResponse } from '../types';
import { MCP, MCPResponse, MarketplaceFilterParams } from '../types/marketplace-items';

// Types for environment details API response
export interface EnvKeyValue {
  key: string;
  value: string;
}

export interface DefinedEnvKey {
  key: string;
  description: string;
}

export interface MCPEnvDetailsResponse {
  success: boolean;
  message: string;
  env_key_values: EnvKeyValue[];
  defined_env_keys: DefinedEnvKey[];
  env_credential_status: string;
  user_mcp_assignment_id: string;
}

// Types for container creation API
export interface CreateContainerRequest {
  mcp_id: string;
  user_id: string;
  type: "stdio";
  env?: EnvKeyValue[];
}

export interface CreateContainerResponse {
  success: boolean;
  message: string;
  container_id: string;
}

/**
 * MCP Service
 * Handles API calls to the MCP templates endpoints
 */
export const mcpService = {
  /**
   * Get all MCPs with optional filtering
   * @param params Optional filter parameters
   * @returns Promise with LegacyMCPResponse (for backward compatibility)
   */
  getMCPs: async (params?: MarketplaceFilterParams): Promise<ApiResponse<MCPResponse>> => {
    try {
      // Convert params to match the API's expected format
      const apiParams: Record<string, any> = {};

      if (params) {
        // Map the params to the API's expected format
        if (params.page) apiParams.page = params.page;
        if (params.page_size) apiParams.page_size = params.page_size;
        // Use page_size if available, otherwise use limit for backward compatibility
        else if (params.limit) apiParams.page_size = params.limit;

        if (params.search) apiParams.search = params.search;
        if (params.category) apiParams.category = params.category;
        if (params.tags) apiParams.tags = params.tags;

        if (params.visibility) apiParams.visibility = params.visibility;
        if (params.status) apiParams.status = params.status;
      }

      console.log('API params for /mcps endpoint:', apiParams);

      const response = await axiosClient.get<MCPResponse>('/mcps', { params: apiParams });
      console.log('MCP API Response:', response);

      // Return the actual API response without transformation
      return {
        data: response.data,
        status: response.status,
        message: 'MCPs retrieved successfully',
      };
    } catch (error: unknown) {
      console.error('Error fetching MCPs:', error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get a single MCP by ID
   * @param id MCP ID
   * @param userId Optional user ID to check if the item is already added
   * @returns Promise with MCP
   */
  getMCPById: async (id: string, userId?: string): Promise<ApiResponse<MCP>> => {
    try {
      // Add userId to params if provided
      const params = userId ? { user_id: userId } : undefined;
      const response = await axiosClient.get(`/mcps/${id}`, { params });
      console.log(`Fetching MCP with ID ${id}:`, response);

      // Handle different response structures
      let mcpData: MCP;

      if (response.data.template) {
        // Old response structure with template property
        mcpData = response.data.template;
      } else if (response.data.data) {
        // New response structure with data property
        mcpData = response.data.data;
      } else if (response.data.mcp) {
        // MCP detail response structure
        mcpData = response.data.mcp;
      } else {
        // Assume the response is the MCP itself
        mcpData = response.data;
      }

      // Log the extracted MCP data
      console.log(`Extracted MCP data for ID ${id}:`, mcpData);

      return {
        data: mcpData,
        status: response.status,
        message: 'MCP retrieved successfully',
      };
    } catch (error: unknown) {
      console.error(`Error fetching MCP with ID ${id}:`, error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get environment details for a specific MCP
   * @param mcpId MCP ID
   * @returns Promise with MCPEnvDetailsResponse
   */
  getEnvDetails: async (mcpId: string): Promise<ApiResponse<MCPEnvDetailsResponse>> => {
    try {
      // Use general axios client for non-marketplace endpoints
      const response = await generalAxiosClient.get(`/mcps/get-env-details/${mcpId}`);
      console.log(`Fetching env details for MCP ID ${mcpId}:`, response);

      return {
        data: response.data,
        status: response.status,
        message: 'Environment details retrieved successfully',
      };
    } catch (error: unknown) {
      console.error(`Error fetching env details for MCP ID ${mcpId}:`, error);
      throw error;
    }
  },

  /**
   * Create MCP container
   * @param request CreateContainerRequest
   * @returns Promise with CreateContainerResponse
   */
  createContainer: async (request: CreateContainerRequest): Promise<ApiResponse<CreateContainerResponse>> => {
    try {
      // Use general axios client for non-marketplace endpoints
      const response = await generalAxiosClient.post(
        '/mcps/containers/create',
        request,
        {
          headers: {
        'X-Server-Auth-Key': process.env.NEXT_PUBLIC_CREATE_CONTAINER_AUTH_KEY
          }
        }
      );
      console.log(`Creating container for MCP ID ${request.mcp_id}:`, response);

      return {
        data: response.data,
        status: response.status,
        message: 'Container created successfully',
      };
    } catch (error: unknown) {
      console.error(`Error creating container for MCP ID ${request.mcp_id}:`, error);
      throw error;
    }
  }
};
