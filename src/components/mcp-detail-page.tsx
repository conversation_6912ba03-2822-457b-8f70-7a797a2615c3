"use client";

import { BaseDetailPage } from "@/components/base-detail-page";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { MarketplaceItem } from "@/data/marketplace-items";
import { redirectToDeveloperPlatform } from "@/lib/helpers";
import { ArrowRight, ChevronDown, ChevronUp, Play, Settings, AlertTriangle, Zap } from "lucide-react"; // Added Settings, AlertTriangle, Zap
import { useState } from "react";
import { McpConnectionSheet, EnvConfigKey } from "./mcp-connection-sheet"; // Import the new sheet and EnvConfigKey
import { useAuth } from "@/hooks/use-auth"; // Import authentication hook
import { LoginDialog } from "./login-dialog"; // Import login dialog component
import { api } from "@/lib/api"; // Import API
import { useEffect } from "react"; // Import useEffect
import type { CreateContainerRequest } from "@/lib/api/services/mcp.service"; // Import types

// EnvConfigKey interface might be defined in McpConnectionSheet or a shared types file
// For now, assuming it's exported from McpConnectionSheet or defined here if not.
// If not exported from sheet, define it here:
// export interface EnvConfigKey {
//   key: string;
//   description: string;
// }


interface MCPDetailPageProps {
  item: MarketplaceItem;
  relatedItems?: MarketplaceItem[];
  refetch?: () => void;
  // isMcpConnected prop removed, managed locally
}

interface ToolParameter {
  type: string;
  description: string;
}

interface SchemaProperties {
  [key: string]: ToolParameter;
}

interface ToolSchema {
  type: string;
  properties: SchemaProperties;
  required?: string[];
}

interface Tool {
  name: string;
  description: string;
  input_schema?: ToolSchema;
  output_schema?: ToolSchema;
}

export function MCPDetailPage({ item, relatedItems = [], refetch }: MCPDetailPageProps) {
  const [installMethod, setInstallMethod] = useState<'json' | 'url'>('json');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [expandedTool, setExpandedTool] = useState<string | null>(null);

  const [isMcpConnected, setIsMcpConnected] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false); // Add login dialog state
  const [isLoadingEnvDetails, setIsLoadingEnvDetails] = useState(false);
  const [envConfigKeys, setEnvConfigKeys] = useState<EnvConfigKey[]>([]);
  const [userEnvValues, setUserEnvValues] = useState<Record<string, string>>({});
  // envValues for the sheet will be managed by the sheet itself or passed if needed

  // Use authentication hook
  const { isAuthenticated, user } = useAuth();

  const tools: Tool[] = item.rawData?.mcp_tools_config?.tools || [];
  // Get initial env keys from item data as fallback
  const initialEnvConfigKeys: EnvConfigKey[] = item.rawData?.env_keys || [];

  // Initialize env keys from item data on component mount
  useEffect(() => {
    if (initialEnvConfigKeys.length > 0) {
      setEnvConfigKeys(initialEnvConfigKeys);
    }
  }, [initialEnvConfigKeys]);

  const navigateToTool = (toolName: string) => {
    setActiveTab('tools');
    setExpandedTool(toolName);
  };

  const handleRunTool = (toolName: string) => {
    console.log(`Run tool: ${toolName} (Connected: ${isMcpConnected})`);
    // Actual tool execution logic will go here
  };

  // Function to fetch environment details
  const fetchEnvDetails = async () => {
    try {
      setIsLoadingEnvDetails(true);
      const response = await api.mcp.getEnvDetails(item.id);

      if (response.data.success) {
        // Set the defined env keys from API response
        setEnvConfigKeys(response.data.defined_env_keys);

        // Set user's existing env values
        const existingValues: Record<string, string> = {};
        response.data.env_key_values.forEach(envValue => {
          existingValues[envValue.key] = envValue.value;
        });
        setUserEnvValues(existingValues);

        console.log('Environment details fetched:', response.data);
      }
    } catch (error) {
      console.error('Error fetching environment details:', error);
      // Fallback to using env keys from item data
      setEnvConfigKeys(initialEnvConfigKeys);
    } finally {
      setIsLoadingEnvDetails(false);
    }
  };

  const handleOpenConnectionSheet = async () => {
    // Check if user is authenticated first
    if (!isAuthenticated) {
      // Show login dialog if not authenticated
      setShowLoginDialog(true);
      return;
    }

    // If authenticated, fetch environment details and open the connection sheet
    await fetchEnvDetails();
    setIsSheetOpen(true);
  };

  // Close the login dialog
  const closeLoginDialog = () => {
    setShowLoginDialog(false);
  };

  const handleSheetConnect = async (submittedEnvValues: Record<string, string>) => {
    if (!user?.id) {
      console.error('User ID not available');
      return;
    }

    setIsConnecting(true);
    console.log("Attempting to connect with (MCPDetailPage):", submittedEnvValues);

    try {
      // Determine which case we're in and prepare the request
      const envCredentialStatus = item.rawData?.env_credential_status;
      const hasExistingValues = Object.keys(userEnvValues).length > 0;

      // Check if any values have been modified
      const hasModifiedValues = envConfigKeys.some(key => {
        const currentValue = submittedEnvValues[key.key] || '';
        const existingValue = userEnvValues[key.key] || '';
        return currentValue !== existingValue;
      });

      // Prepare the container creation request
      const createContainerRequest: CreateContainerRequest = {
        mcp_id: item.id,
        user_id: user.id,
        type: "stdio" as const,
      };

      // Case 1: User doesn't have any env values and status is pending_input
      // Case 3: User has modified values
      if ((!hasExistingValues && envCredentialStatus === "pending_input") || hasModifiedValues) {
        // Include env values in the request
        const envArray = envConfigKeys
          .filter(key => submittedEnvValues[key.key]) // Only include keys with values
          .map(key => ({
            key: key.key,
            value: submittedEnvValues[key.key]
          }));

        if (envArray.length > 0) {
          createContainerRequest.env = envArray;
        }
      }
      // Case 2: User has existing values and hasn't modified them - don't include env

      console.log('Container creation request:', createContainerRequest);

      // Call the API to create container
      const response = await api.mcp.createContainer(createContainerRequest);

      if (response.data.success) {
        setIsMcpConnected(true);
        setIsSheetOpen(false);
        console.log('Container created successfully:', response.data.container_id);
        // toast.success("MCP Server Connected!"); // Example
      } else {
        setIsMcpConnected(false);
        console.error('Container creation failed:', response.data.message);
        // toast.error("Connection Failed: " + response.data.message); // Example
      }
    } catch (error) {
      console.error('Error creating container:', error);
      setIsMcpConnected(false);
      // toast.error("Connection Failed. Please try again."); // Example
    } finally {
      setIsConnecting(false);
    }
  };
  

  return (
    <BaseDetailPage
      item={item}
      relatedItems={relatedItems}
      refetch={refetch}
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full mb-4 justify-start gap-2">
          <TabsTrigger value="overview" className="w-20">Overview</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <p className="text-muted-foreground mb-4">
              {item.description}
            </p>
          </div>

          {/* Tools Overview Section */}
          {tools.length > 0 ? (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Available Tools</h2>
              <div className="grid gap-4 md:grid-cols-2">
                {tools.map((tool) => (
                  <Card
                    key={tool.name}
                    className="group border border-gray-200 dark:border-gray-800 hover:border-primary/50 dark:hover:border-primary/50 transition-all cursor-pointer"
                    onClick={() => navigateToTool(tool.name)}
                  >
                    <CardContent className="pt-6 flex justify-between items-start">
                      <div className="flex-1 pr-4">
                        <h3 className="font-medium mb-2 text-primary group-hover:text-primary/80">{tool.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {tool.description}
                        </p>
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <ArrowRight className="h-5 w-5 text-primary" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) :null}
        </TabsContent>

        {/* Tools Tab Content */}
        <TabsContent value="tools">
          <div className="mb-8">
            {/* Connection Banner - Only shown in 'tools' tab and if not connected */}
            {activeTab === 'tools' && !isMcpConnected && (
              <Card className="mb-6 bg-amber-50 border-amber-200 dark:bg-amber-900/30 dark:border-amber-700">
                <CardContent className="pt-6 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-6 w-6 text-amber-500 dark:text-amber-400" />
                    <div>
                      <h3 className="font-semibold text-amber-700 dark:text-amber-300">Server Connection Required</h3>
                      <p className="text-sm text-amber-600 dark:text-amber-400/80">
                        Connect to your MCP server to test tools.
                      </p>
                    </div>
                  </div>
                  <Button onClick={handleOpenConnectionSheet} variant="default" className="bg-primary hover:bg-primary/90">
                    <Settings className="mr-2 h-4 w-4" /> Connect
                  </Button>
                </CardContent>
              </Card>
            )}
             {activeTab === 'tools' && isMcpConnected && (
                <Card className="mb-6 bg-green-50 border-green-200 dark:bg-green-900/30 dark:border-green-700">
                <CardContent className="pt-6 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Zap className="h-6 w-6 text-green-500 dark:text-green-400" />
                    <div>
                      <h3 className="font-semibold text-green-700 dark:text-green-300">Server Connected</h3>
                      <p className="text-sm text-green-600 dark:text-green-400/80">
                        You can now run the available tools.
                      </p>
                    </div>
                  </div>
                  <Button onClick={handleOpenConnectionSheet} variant="outline">
                    <Settings className="mr-2 h-4 w-4" /> Configure
                  </Button>
                </CardContent>
              </Card>
            )}


            <h2 className="text-xl font-semibold mb-4">Available Tools</h2>
            <p className="text-muted-foreground mb-6">
              This MCP provides the following tools and capabilities.
            </p>

            {/* Tools List */}
            <div className="space-y-4">
              {tools.length > 0 ? (
                tools.map((tool) => {
                  const isExpanded = expandedTool === tool.name;
                  const parametersToDisplay = tool.input_schema?.properties || {};
                  const hasParametersToDisplay = Object.keys(parametersToDisplay).length > 0;

                  return (
                    <Card
                      key={tool.name}
                      className={`border ${isExpanded ? 'border-primary/50' : 'border-gray-200 dark:border-gray-800'} transition-all`}
                    >
                      <CardContent className="pt-6">
                        <div
                          className="flex justify-between items-center cursor-pointer"
                          onClick={() => setExpandedTool(isExpanded ? null : tool.name)}
                        >
                          <div>
                            <h3 className="font-medium text-primary">{tool.name}</h3>
                            <p className="text-sm text-muted-foreground mt-1">
                              {tool.description}
                            </p>
                          </div>
                          <div>
                            {isExpanded ? (
                              <ChevronUp className="h-5 w-5 text-muted-foreground" />
                            ) : (
                              <ChevronDown className="h-5 w-5 text-muted-foreground" />
                            )}
                          </div>
                        </div>

                        {isExpanded && (
                          <div className="mt-4 pt-4 border-t">
                            {hasParametersToDisplay && (
                              <>
                                <h4 className="text-sm font-medium mb-3">Parameters</h4>
                                <div className="space-y-4 mb-4">
                                  {Object.entries(parametersToDisplay).map(([paramName, param]) => (
                                    <div key={paramName} className="space-y-2">
                                      <div className="flex items-center gap-1">
                                        <span className="text-sm font-medium">{paramName}</span>
                                        {tool.input_schema?.required?.includes(paramName) && (
                                          <span className="text-xs text-red-500">*</span>
                                        )}
                                      </div>
                                      <Input
                                        disabled // Parameters are for display, not input here
                                        placeholder={param.description}
                                        className="text-sm text-muted-foreground bg-muted"
                                      />
                                      <div className="flex gap-2 text-xs text-muted-foreground">
                                        <span>Type: {param.type}</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                             <Button
                                onClick={() => handleRunTool(tool.name)}
                                disabled={!isMcpConnected} // Use the state updated by BaseDetailPage
                                className="w-full"
                              >
                                <Play className="mr-2 h-4 w-4" />
                                Run Tool
                              </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-muted-foreground">
                      No tools available for this MCP.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Integration Tab Content */}
        <TabsContent value="integration">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">API Integration</h2>
            <p className="text-muted-foreground mb-4">
              Integrate this MCP into your applications.
            </p>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Get your API Key</h3>
              <p className="text-muted-foreground mb-3">
                You&apos;ll need to sign in to the RUH Developer Portal and generate an API key to connect to this server.
              </p>
              <Button
                variant="secondary"
                className="bg-gray-800 hover:bg-gray-700 text-white dark:bg-primary/90 dark:hover:bg-primary dark:text-white dark:border-primary/30"
                onClick={redirectToDeveloperPlatform}
              >
                Generate API Key
              </Button>
            </div>

            {/* Installation Details Section - Moved from sidebar */}
            <div className="mt-8 mb-8 border-t pt-4">
              <h3 className="text-lg font-medium mb-4">Installation Details</h3>
              <div className="space-y-4">
                <div className="flex gap-2 justify-start">
                  <Button
                    variant={installMethod === 'json' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setInstallMethod('json')}
                    className="w-24"
                  >
                    JSON
                  </Button>
                  <Button
                    variant={installMethod === 'url' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setInstallMethod('url')}
                    className="w-24"
                  >
                    URL
                  </Button>
                </div>

                <div className="bg-muted p-3 rounded-md text-xs font-mono overflow-x-auto">
                  {installMethod === 'json' ? (
                    <pre>{`{
  "id": "${item.id}",
  "name": "${item.title}",
  "type": "${item.type.toLowerCase()}"
}`}</pre>
                  ) : (
                    <pre>{`https://api.ruh.ai/marketplace/${item.type.toLowerCase()}/${item.id}`}</pre>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-8">
              <h3 className="text-lg font-medium mb-3">Code Example</h3>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
                  <code>{`// JavaScript Example
const fetchData = async () => {
  try {
    const response = await axios.get('https://api.ruh.ai/mcp/${item.id}', {
      headers: {
        'Authorization': 'Bearer your-ruh-api-key',
        'Content-Type': 'application/json'
      }
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};`}</code>
                </pre>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
      <McpConnectionSheet
        isOpen={isSheetOpen}
        onOpenChange={setIsSheetOpen}
        envConfigKeys={envConfigKeys}
        onConnect={handleSheetConnect}
        isConnected={isMcpConnected}
        isLoading={isConnecting}
        mcpTitle={item.title}
        userEnvValues={userEnvValues}
      />

      {/* Login Dialog */}
      <LoginDialog
        isOpen={showLoginDialog}
        onOpenChange={closeLoginDialog}
      />
    </BaseDetailPage>
  );
}
